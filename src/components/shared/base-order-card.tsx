'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { useLocalStorage } from 'usehooks-ts';

import { TgsOrImage } from '@/components/TgsOrImage';
import { Card, CardContent } from '@/components/ui/card';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';

interface BaseOrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  animated?: boolean;
  children: React.ReactNode;
}

export function BaseOrderCard({
  animated,
  order,
  collection,
  onClick,
  children,
}: BaseOrderCardProps) {
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col">
        <div className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]">
          <TgsOrImage
            isImage={!isAnimatedCollection}
            collectionId={order.collectionId}
            imageProps={{
              alt: collection?.name || 'Order item',
              fill: true,
              className:
                'object-cover group-hover:scale-105 transition-transform duration-200',
            }}
            tgsProps={{
              style: { height: 'auto', width: 'auto', padding: '16px' },
            }}
          />
        </div>

        <div className="flex items-center justify-between mt-2 mb-2">
          <Caption level="1" weight="1" className="truncate">
            {collection?.name || 'Unknown Collection'}
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ||
              (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
          </Caption>
        </div>

        {children}
      </CardContent>
    </Card>
  );
}
