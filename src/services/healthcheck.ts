import { redisService } from "./redis";
import { log } from "../utils/logger";

export class HealthcheckService {
  private static readonly HEALTHCHECK_KEY = "healthcheck";
  private static healthcheckInterval: NodeJS.Timeout | null = null;

  static async writeInitHealthcheck(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      await redisService.set(this.HEALTHCHECK_KEY, timestamp);
      log.healthLog(
        `Bot initialization healthcheck written to Redis: ${timestamp}`,
        {
          status: "init_healthcheck_written",
          timestamp,
        }
      );
    } catch (error) {
      log.error("Failed to write healthcheck to Redis", error, {
        operation: "healthcheck_init",
      });
      throw error;
    }
  }

  static startPeriodicHealthcheck(): void {
    // Update healthcheck every 5 minutes
    this.healthcheckInterval = setInterval(async () => {
      try {
        const timestamp = new Date().toISOString();
        await redisService.set(this.HEALTHCHECK_KEY, timestamp);
        log.healthLog(`Periodic healthcheck updated: ${timestamp}`, {
          status: "periodic_healthcheck_updated",
          timestamp,
        });
      } catch (error) {
        log.error("Failed to update periodic healthcheck", error, {
          operation: "healthcheck_periodic",
        });
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  static stopPeriodicHealthcheck(): void {
    if (this.healthcheckInterval) {
      clearInterval(this.healthcheckInterval);
      this.healthcheckInterval = null;
      log.healthLog("Periodic healthcheck stopped", {
        status: "periodic_healthcheck_stopped",
      });
    }
  }

  static async getLastHealthcheck(): Promise<string | null> {
    try {
      return await redisService.get(this.HEALTHCHECK_KEY);
    } catch (error) {
      log.error("Failed to read healthcheck from Redis", error, {
        operation: "healthcheck_read",
      });
      return null;
    }
  }

  static async isHealthy(): Promise<boolean> {
    try {
      const lastHealthcheck = await this.getLastHealthcheck();
      if (!lastHealthcheck) {
        return false;
      }

      const lastHealthcheckTime = new Date(lastHealthcheck);
      const now = new Date();
      const timeDiff = now.getTime() - lastHealthcheckTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      // Consider healthy if last healthcheck was within 24 hours
      return hoursDiff < 24;
    } catch (error) {
      log.error("Failed to check health status", error, {
        operation: "healthcheck_status",
      });
      return false;
    }
  }
}
