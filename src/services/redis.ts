import { createClient, RedisClientType } from "redis";
import { log } from "../utils/logger";
import dotenv from "dotenv";

dotenv.config();

class RedisService {
  private client: RedisClientType | null = null;
  private isConnected = false;

  async connect(): Promise<void> {
    if (this.isConnected && this.client) {
      return;
    }

    try {
      const redisHost =
        process.env.REDIS_HOST ?? process.env.REDIS_HOST_LOCAL ?? "localhost";
      const redisPort =
        process.env.REDIS_PORT ?? process.env.REDIS_PORT_LOCAL ?? "6379";
      const redisUrl = `redis://${redisHost}:${redisPort}`;

      this.client = createClient({
        url: redisUrl,
        socket: {
          reconnectStrategy: (retries) => {
            log.warn(`Redis reconnect attempt #${retries}`, {
              operation: "redis_reconnect",
              retries,
            });
            // wait 2 seconds before retrying, limit to 10 retries
            if (retries > 10) return new Error("Too many retries");
            return 2000;
          },
          connectTimeout: 30000,
        },
      });

      this.client.on("error", (err) => {
        // Log Redis configuration details
        log.redisLog("Redis Client Error", {
          operation: "redis_error",
          error: err,
          config: {
            REDIS_HOST: process.env.REDIS_HOST,
            REDIS_PORT: process.env.REDIS_PORT,
            REDIS_HOST_LOCAL: process.env.REDIS_HOST_LOCAL,
            REDIS_PORT_LOCAL: process.env.REDIS_PORT_LOCAL,
            resolvedHost: redisHost,
            resolvedPort: redisPort,
            finalUrl: redisUrl,
          },
        });
        this.isConnected = false;
      });

      this.client.on("connect", () => {
        log.redisLog("Redis connected", {
          operation: "redis_connect",
          status: "connected",
        });
        this.isConnected = true;
      });

      this.client.on("disconnect", () => {
        log.redisLog("Redis disconnected", {
          operation: "redis_disconnect",
          status: "disconnected",
        });
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      log.error("Failed to connect to Redis", error, {
        operation: "redis_connect",
      });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
      this.client = null;
    }
  }

  private ensureConnected(): void {
    if (!this.client || !this.isConnected) {
      throw new Error("Redis client is not connected");
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    this.ensureConnected();

    if (ttlSeconds) {
      await this.client!.setEx(key, ttlSeconds, value);
    } else {
      await this.client!.set(key, value);
    }
  }

  async get(key: string): Promise<string | null> {
    this.ensureConnected();
    return await this.client!.get(key);
  }

  async del(key: string): Promise<number> {
    this.ensureConnected();
    return await this.client!.del(key);
  }

  async exists(key: string): Promise<number> {
    this.ensureConnected();
    return await this.client!.exists(key);
  }

  async expire(key: string, seconds: number): Promise<number> {
    this.ensureConnected();
    return await this.client!.expire(key, seconds);
  }

  async keys(pattern: string): Promise<string[]> {
    this.ensureConnected();
    return await this.client!.keys(pattern);
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

export const redisService = new RedisService();
