import './globals.css';

import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { Toaster } from 'sonner';

import { RootProvider } from '@/root-context';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'PREM',
  description: 'Telegram Gifts Marketplace',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        url: '/android-chrome-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        url: '/android-chrome-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
  },
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#17212b] text-[#f5f5f5]`}
        suppressHydrationWarning
      >
        <RootProvider>{children}</RootProvider>

        <Toaster
          theme="dark"
          position='top-center'
          toastOptions={{
            style: {
              background: '#232e3c',
              border: '1px solid #3a4a5c',
              color: '#f5f5f5',
            },
          }}
        />
      </body>
    </html>
  );
}
