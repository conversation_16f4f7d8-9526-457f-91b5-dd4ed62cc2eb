import type { LucideIcon } from 'lucide-react';
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>ircle,
  DollarSign,
  Gift,
  RefreshCw,
  RotateCcw,
  ShoppingCart,
  XCircle,
} from 'lucide-react';

import type { AdminOrderStats } from '@/api/admin-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StatCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  description: string;
  onUpdate?: () => void;
  isUpdating?: boolean;
}

function StatCard({
  title,
  value,
  icon: Icon,
  description,
  onUpdate,
  isUpdating = false,
}: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-muted-foreground" />
          {onUpdate && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onUpdate}
              disabled={isUpdating}
              className="h-6 w-6 p-0"
            >
              <RotateCcw
                className={`h-3 w-3 ${isUpdating ? 'animate-spin' : ''}`}
              />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

interface AdminOrdersStatsProps {
  stats: AdminOrderStats | null;
  loading: boolean;
  onUpdateStat?: (statType: string) => void;
  updatingStats?: Set<string>;
}

export function AdminOrdersStats({
  stats,
  loading,
  onUpdateStat,
  updatingStats = new Set(),
}: AdminOrdersStatsProps) {
  if (loading || !stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  const totalNonAdminOrders =
    stats.nonAdminOrders.active +
    stats.nonAdminOrders.paid +
    stats.nonAdminOrders.giftSentToRelayer +
    stats.nonAdminOrders.cancelled +
    stats.nonAdminOrders.fulfilled;

  const totalAdminOrders =
    stats.adminOrders.active +
    stats.adminOrders.paid +
    stats.adminOrders.giftSentToRelayer +
    stats.adminOrders.cancelled +
    stats.adminOrders.fulfilled;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Total Orders"
          value={stats.totalOrders}
          icon={BarChart3}
          description="All orders in system"
          onUpdate={() => onUpdateStat?.('totalOrders')}
          isUpdating={updatingStats.has('totalOrders')}
        />
        <StatCard
          title="Non-Admin Orders"
          value={totalNonAdminOrders}
          icon={ShoppingCart}
          description="Orders without admin involvement"
          onUpdate={() => onUpdateStat?.('nonAdminOrders')}
          isUpdating={updatingStats.has('nonAdminOrders')}
        />
        <StatCard
          title="Admin Orders"
          value={totalAdminOrders}
          icon={DollarSign}
          description="Orders with admin involvement"
          onUpdate={() => onUpdateStat?.('adminOrders')}
          isUpdating={updatingStats.has('adminOrders')}
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <StatCard
          title="Active (Non-Admin)"
          value={stats.nonAdminOrders.active}
          icon={RefreshCw}
          description="Active orders without admins"
          onUpdate={() => onUpdateStat?.('nonAdminActive')}
          isUpdating={updatingStats.has('nonAdminActive')}
        />
        <StatCard
          title="Paid (Non-Admin)"
          value={stats.nonAdminOrders.paid}
          icon={DollarSign}
          description="Paid orders without admins"
          onUpdate={() => onUpdateStat?.('nonAdminPaid')}
          isUpdating={updatingStats.has('nonAdminPaid')}
        />
        <StatCard
          title="Gift Sent (Non-Admin)"
          value={stats.nonAdminOrders.giftSentToRelayer}
          icon={Gift}
          description="Gift sent without admins"
          onUpdate={() => onUpdateStat?.('nonAdminGiftSent')}
          isUpdating={updatingStats.has('nonAdminGiftSent')}
        />
        <StatCard
          title="Cancelled (Non-Admin)"
          value={stats.nonAdminOrders.cancelled}
          icon={XCircle}
          description="Cancelled orders without admins"
          onUpdate={() => onUpdateStat?.('nonAdminCancelled')}
          isUpdating={updatingStats.has('nonAdminCancelled')}
        />
        <StatCard
          title="Fulfilled (Non-Admin)"
          value={stats.nonAdminOrders.fulfilled}
          icon={CheckCircle}
          description="Fulfilled orders without admins"
          onUpdate={() => onUpdateStat?.('nonAdminFulfilled')}
          isUpdating={updatingStats.has('nonAdminFulfilled')}
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <StatCard
          title="Active (Admin)"
          value={stats.adminOrders.active}
          icon={RefreshCw}
          description="Active orders with admins"
          onUpdate={() => onUpdateStat?.('adminActive')}
          isUpdating={updatingStats.has('adminActive')}
        />
        <StatCard
          title="Paid (Admin)"
          value={stats.adminOrders.paid}
          icon={DollarSign}
          description="Paid orders with admins"
          onUpdate={() => onUpdateStat?.('adminPaid')}
          isUpdating={updatingStats.has('adminPaid')}
        />
        <StatCard
          title="Gift Sent (Admin)"
          value={stats.adminOrders.giftSentToRelayer}
          icon={Gift}
          description="Gift sent with admins"
          onUpdate={() => onUpdateStat?.('adminGiftSent')}
          isUpdating={updatingStats.has('adminGiftSent')}
        />
        <StatCard
          title="Cancelled (Admin)"
          value={stats.adminOrders.cancelled}
          icon={XCircle}
          description="Cancelled orders with admins"
          onUpdate={() => onUpdateStat?.('adminCancelled')}
          isUpdating={updatingStats.has('adminCancelled')}
        />
        <StatCard
          title="Fulfilled (Admin)"
          value={stats.adminOrders.fulfilled}
          icon={CheckCircle}
          description="Fulfilled orders with admins"
          onUpdate={() => onUpdateStat?.('adminFulfilled')}
          isUpdating={updatingStats.has('adminFulfilled')}
        />
      </div>
    </div>
  );
}
