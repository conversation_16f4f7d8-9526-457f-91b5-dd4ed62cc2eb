'use client';

import { But<PERSON> } from '@telegram-apps/telegram-ui';
import { HandHeart, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import {
  CachePatterns,
  type OrderEntity,
  UserType,
} from '@/constants/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useMarketplaceFilters } from '@/hooks/use-marketplace-filters';
import { useRootContext } from '@/root-context';

import { MarketplaceTabs } from './marketplace/components/marketplace-tabs';
import { CreateOrderDrawer } from './marketplace/create-order-drawer';
import type { TabType } from './marketplace/hooks/use-marketplace-orders';
import { useMarketplaceOrders } from './marketplace/hooks/use-marketplace-orders';
import { OrderDetailsDrawer } from './marketplace/order-details-drawer';
import { ResellOrderDrawer } from './marketplace/resell/resell-order-drawer';

export default function MarketplacePage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [activeTab, setActiveTab] = useState<TabType>('sellers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [showResellDrawer, setShowResellDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  const filters = useMarketplaceFilters();
  const ordersFilters = {
    ...filters.getFilters(),
    currentUserId: currentUser?.id,
  };

  const { sellersState, buyersState, loadOrders, loadMoreOrders } =
    useMarketplaceOrders({
      activeTab,
      filters: ordersFilters,
    });

  const sellersLoadMoreRef = useInfiniteScroll({
    hasMore: sellersState.hasMore,
    loading: sellersState.loading || sellersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'sellers') {
        loadMoreOrders();
      }
    },
  });

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersState.hasMore,
    loading: buyersState.loading || buyersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'buyers') {
        loadMoreOrders();
      }
    },
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, currentUser]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders(true);
    refetchUser();
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = handleOrderCreated;

  const handleResellClick = () => {
    setShowResellDrawer(true);
  };

  const handleOrderResold = () => {
    handleOrderCreated();
  };

  return (
    <div className="relative space-y-2 bg-[#17212b] min-h-screen">
      {/* Resell button - only show on buyers tab */}
      {activeTab === 'buyers' && (
        <Button
          onClick={handleResellClick}
          className="fixed! rounded-[50%]! bottom-[120px] right-2 w-[42px]! h-[42px]! p-0! flex justify-center bg-green-500 hover:bg-green-600"
        >
          <HandHeart size={20} />
        </Button>
      )}

      {/* Create order button */}
      <Button
        onClick={handleCreateOrder}
        className="fixed! rounded-[50%]! bottom-16 right-2 w-[42px]! h-[42px]! p-0! flex justify-center"
      >
        <Plus />
      </Button>
      <Tabs value={activeTab}>
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters
          minPrice={filters.minPrice}
          maxPrice={filters.maxPrice}
          selectedCollection={filters.selectedCollection}
          sortBy={filters.sortBy}
          collections={collections}
          onMinPriceChange={filters.setMinPrice}
          onMaxPriceChange={filters.setMaxPrice}
          onCollectionChange={filters.setSelectedCollection}
          onSortChange={filters.setSortBy}
        />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={sellersState.orders}
            collections={collections}
            loading={sellersState.loading}
            loadingMore={sellersState.loadingMore}
            emptyMessage="No orders found for sellers"
            onOrderClick={handleOrderClick}
            ref={sellersLoadMoreRef}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={buyersState.orders}
            collections={collections}
            loading={buyersState.loading}
            loadingMore={buyersState.loadingMore}
            emptyMessage="No orders found for buyers"
            onOrderClick={handleOrderClick}
            ref={buyersLoadMoreRef}
          />
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderAction={handleOrderAction}
      />

      <ResellOrderDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        collections={collections}
        onOrderResold={handleOrderResold}
      />
    </div>
  );
}
