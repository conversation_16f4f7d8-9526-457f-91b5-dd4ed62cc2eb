'use client';

import { Badge } from '@/components/ui/badge';
import { BaseOrderCard } from '@/components/shared/base-order-card';
import { PriceButton, PriceRow } from '@/components/shared/price-display';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
}

const isSecondaryMarketOrder = (order: OrderEntity): boolean => {
  return (
    order.status === 'paid' &&
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};

export function OrderCard({
  order,
  collection,
  onClick,
}: OrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);

  return (
    <BaseOrderCard
      order={order}
      collection={collection}
      onClick={onClick}
    >
      {isSecondary && (
        <div className="mb-2">
          <Badge
            variant="secondary"
            className="bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30 text-xs"
          >
            🔄 Secondary Market
          </Badge>
        </div>
      )}

      {isSecondary ? (
        <>
          <div className="space-y-1 mb-2">
            <PriceRow
              label="Primary:"
              amount={order.price}
              className="text-[#708499]"
            />
            <PriceRow
              label="Secondary:"
              amount={order.secondaryMarketPrice || 0}
              className="text-[#6ab2f2]"
              tonLogoClassName="text-[#6ab2f2]"
            />
          </div>
          <PriceButton amount={order.secondaryMarketPrice || 0} />
        </>
      ) : (
        <PriceButton amount={order.price} />
      )}
    </BaseOrderCard>
  );
}
