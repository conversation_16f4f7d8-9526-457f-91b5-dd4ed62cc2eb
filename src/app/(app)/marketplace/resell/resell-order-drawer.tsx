'use client';

import { Spinner } from '@telegram-apps/telegram-ui';
import { TrendingUp } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { getEligibleOrdersForResale } from '@/api/order-api';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerActions } from '@/components/ui/drawer/drawer-actions';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

import { ResellOrderList } from './resell-order-list';
import { ResellOrderPriceDrawer } from './resell-order-price-drawer';

interface ResellOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collections: CollectionEntity[];
  onOrderResold: () => void;
}

export function ResellOrderDrawer({
  open,
  onOpenChange,
  collections,
  onOrderResold,
}: ResellOrderDrawerProps) {
  const { currentUser } = useRootContext();
  const [eligibleOrders, setEligibleOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showPriceDrawer, setShowPriceDrawer] = useState(false);

  const loadEligibleOrders = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      const orders = await getEligibleOrdersForResale(currentUser.id);
      setEligibleOrders(orders);
    } catch (error) {
      console.error('Error loading eligible orders:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  useEffect(() => {
    if (open && currentUser?.id) {
      loadEligibleOrders();
    }
  }, [open, currentUser?.id, loadEligibleOrders]);

  const handleOrderSelect = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowPriceDrawer(true);
  };

  const handlePriceDrawerClose = () => {
    setShowPriceDrawer(false);
    setSelectedOrder(null);
  };

  const handleOrderResold = () => {
    setShowPriceDrawer(false);
    setSelectedOrder(null);
    onOpenChange(false);
    onOrderResold();
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <>
      <BaseDrawer
        open={open}
        onOpenChange={onOpenChange}
        zIndex={50}
        height="h-[80vh]"
        className="mt-16 z-50"
      >
        <DrawerHeader
          icon={TrendingUp}
          title="Resell My Order"
          subtitle="Select an order you purchased to resell on the secondary market"
        />

        {loading ? (
          <div className="text-center py-8">
            <Spinner className="flex justify-center" size="l" />
            <p className="text-[#708499] mt-2">Loading your orders...</p>
          </div>
        ) : eligibleOrders.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-[#708499]">
              You can't create resell order, because you do not have orders you
              bought.
            </p>
          </div>
        ) : (
          <ResellOrderList
            orders={eligibleOrders}
            collections={collections}
            onOrderSelect={handleOrderSelect}
          />
        )}

        <DrawerActions
          onPrimary={handleClose}
          onCancel={handleClose}
          primaryLabel="Close"
          cancelLabel="Cancel"
          primaryClassName="bg-[#3a4a5c] hover:bg-[#4a5a6c] text-[#f5f5f5]"
        />
      </BaseDrawer>

      <ResellOrderPriceDrawer
        open={showPriceDrawer}
        onOpenChange={handlePriceDrawerClose}
        order={selectedOrder}
        onOrderResold={handleOrderResold}
      />
    </>
  );
}
