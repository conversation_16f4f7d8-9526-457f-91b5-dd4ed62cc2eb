'use client';

import { useState } from 'react';
import { Drawer } from 'vaul';

import { Badge } from '@/components/ui/badge';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useOtherUser } from '@/hooks/use-other-user';
import { useRootContext } from '@/root-context';
import { getStatusConfig } from '@/utils/order-status-utils';
import { shouldShowUserInfo } from '@/utils/order-utils';

import { ResellOrderPriceDrawer } from '../marketplace/resell/resell-order-price-drawer';
import { CancelOrderDrawer } from './cancel-order-drawer';
import {
  UserOrderActionsSection,
  UserOrderDeadlineSection,
  UserOrderDetailsSection,
  UserOrderImageSection,
  UserOrderPricingSection,
  UserOrderStatusAlerts,
  UserOrderUserInfoSection,
} from './user-order-details-drawer/';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType: UserType;
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { collections, currentUser } = useRootContext();
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showResellPriceDrawer, setShowResellPriceDrawer] = useState(false);

  const collection =
    collections.find((c) => c.id === order?.collectionId) || null;
  const { timeLeft, isFreezed } = useOrderTimers({ order, collection });
  const { otherUser, loadingUser } = useOtherUser({
    order,
    userType,
    isOpen: open,
  });

  const handleCancelOrder = () => setShowCancelDrawer(true);
  const handleCreateSecondaryMarketOrder = () => setShowResellPriceDrawer(true);

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleOrderResold = () => {
    onOrderUpdate();
    setShowResellPriceDrawer(false);
  };

  if (!order) return null;

  return (
    <>
      <Drawer.Root open={open} onOpenChange={onOpenChange}>
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
          <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] h-[85vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
            <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto space-y-6">
                <div className="text-center space-y-2">
                  <h2 className="text-xl font-bold text-[#f5f5f5]">
                    Order Details
                  </h2>
                  <UserOrderStatusBadge order={order} />
                </div>

                <UserOrderImageSection collection={collection} />

                <UserOrderPricingSection
                  order={order}
                  collection={collection}
                />

                <UserOrderDetailsSection order={order} userType={userType} />

                {(order.status === OrderStatus.PAID ||
                  order.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
                  <div className="space-y-4">
                    <UserOrderDeadlineSection
                      {...{
                        order,
                        userType,
                        timeLeft,
                      }}
                    />
                    <UserOrderStatusAlerts
                      {...{
                        order,
                        userType,
                        isFreezed,
                      }}
                    />
                  </div>
                )}

                {shouldShowUserInfo(order) && (
                  <UserOrderUserInfoSection
                    {...{
                      otherUser,
                      userType,
                      loadingUser,
                    }}
                  />
                )}

                <UserOrderActionsSection
                  order={order}
                  currentUserId={currentUser?.id}
                  onCancelOrder={handleCancelOrder}
                  onCreateSecondaryMarketOrder={
                    handleCreateSecondaryMarketOrder
                  }
                />
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={order}
        onOrderCancelled={handleOrderCancelled}
      />

      <ResellOrderPriceDrawer
        open={showResellPriceDrawer}
        onOpenChange={setShowResellPriceDrawer}
        order={order}
        onOrderResold={handleOrderResold}
      />
    </>
  );
}

interface UserOrderStatusBadgeProps {
  order: OrderEntity;
}

export function UserOrderStatusBadge({ order }: UserOrderStatusBadgeProps) {
  const statusConfig = getStatusConfig(order.status);

  return (
    <Badge variant="outline" className={statusConfig.className}>
      {statusConfig.label}
    </Badge>
  );
}
