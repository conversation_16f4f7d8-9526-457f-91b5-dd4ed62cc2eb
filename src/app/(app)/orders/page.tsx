'use client';

import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { getUserOrders } from '@/api/order-api';
import { VirtualizedCard } from '@/components/ui/virtualized-card';
import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type { OrderEntity } from '@/constants/core.constants';
import { UserType } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

import { UserOrderDetailsDrawer } from './user-order-details-drawer';

export default function OrdersPage() {
  const { currentUser } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);

  const fetchUserOrders = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      const userOrders = await getUserOrders(currentUser.id);
      setOrders(userOrders);
    } catch (error) {
      console.error('Error fetching user orders:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  useEffect(() => {
    fetchUserOrders();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderUpdate = () => {
    fetchUserOrders();
  };

  const getUserRole = (order: OrderEntity) => {
    return order.sellerId === currentUser?.id
      ? UserType.SELLER
      : UserType.BUYER;
  };

  const buyOrders = orders.filter(
    (order) => getUserRole(order) === UserType.BUYER,
  );
  const sellOrders = orders.filter(
    (order) => getUserRole(order) === UserType.SELLER,
  );

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-[#708499]">Please log in to view your orders</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-[75px]">
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-[#708499]">No orders found</p>
        </div>
      ) : (
        <ItemCacheProvider>
          <div className="space-y-6">
            {buyOrders.length > 0 && (
              <div>
                <h2 className="text-lg font-semibold text-[#f5f5f5] mb-4 px-1">
                  My Buy Orders ({buyOrders.length})
                </h2>
                <div className="grid grid-cols-2 gap-3">
                  {buyOrders.map((order, index) => (
                    <VirtualizedCard
                      variant="user-order"
                      key={order.id}
                      order={order}
                      userRole={UserType.BUYER}
                      onClick={() => handleOrderClick(order)}
                      index={index}
                      initialRenderedCount={8}
                    />
                  ))}
                </div>
              </div>
            )}

            {sellOrders.length > 0 && (
              <div>
                <h2 className="text-lg font-semibold text-[#f5f5f5] mb-4 px-1">
                  My Sell Orders ({sellOrders.length})
                </h2>
                <div className="grid grid-cols-2 gap-3">
                  {sellOrders.map((order, index) => (
                    <VirtualizedCard
                      variant="user-order"
                      key={order.id}
                      order={order}
                      userRole={UserType.SELLER}
                      onClick={() => handleOrderClick(order)}
                      index={index}
                      initialRenderedCount={8}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </ItemCacheProvider>
      )}

      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={selectedOrder ? getUserRole(selectedOrder) : UserType.BUYER}
        onOrderUpdate={handleOrderUpdate}
      />
    </div>
  );
}
