import bot from "./bot";
import { redisService } from "./services/redis";
import { HealthcheckService } from "./services/healthcheck";
import { expressHttpServer } from "./services/express-server";
import { loadEnvironment } from "./config/env-loader";
import { log } from "./utils/logger";

loadEnvironment();

const PORT = process.env.PORT ?? 8080;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;

// Log startup configuration for debugging
log.info("Startup Configuration", {
  operation: "bot_startup",
  NODE_ENV,
  PORT,
  WEBHOOK_URL: WEBHOOK_URL ? WEBHOOK_URL.substring(0, 50) + "..." : "Not set",
  processId: process.pid,
});

// Validate environment variables for production
if (NODE_ENV === "production") {
  if (!WEBHOOK_URL) {
    log.error("WEBHOOK_URL is required for production deployment", undefined, {
      operation: "bot_startup",
      NODE_ENV,
    });
    process.exit(1);
  }

  if (!WEBHOOK_URL.startsWith("https://")) {
    log.error("WEBHOOK_URL must use HTTPS in production", undefined, {
      operation: "bot_startup",
      NODE_ENV,
      WEBHOOK_URL,
    });
    process.exit(1);
  }

  log.info("Production environment variables validated", {
    operation: "bot_startup",
    NODE_ENV,
  });
}

async function setupWebhook() {
  try {
    const webhookUrl = `${WEBHOOK_URL}/webhook`;

    // Delete any existing webhook first
    await bot.telegram.deleteWebhook({ drop_pending_updates: true });
    log.webhookLog("Cleared existing webhook and dropped pending updates", {
      operation: "webhook_setup",
      webhookUrl,
    });

    // Wait a moment for the deletion to take effect
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Set the new webhook with configuration
    await bot.telegram.setWebhook(webhookUrl, {
      max_connections: 40,
      allowed_updates: [
        "message",
        "callback_query",
        "inline_query",
        "chosen_inline_result",
        "edited_message",
        "channel_post",
        "edited_channel_post",
      ],
    });
    log.webhookLog(`Webhook set to: ${webhookUrl}`, {
      operation: "webhook_setup",
      webhookUrl,
      status: "success",
    });

    // Verify webhook is set correctly
    const webhookInfo = await bot.telegram.getWebhookInfo();
    log.webhookLog("Webhook info retrieved", {
      operation: "webhook_setup",
      url: webhookInfo?.url as string,
      has_custom_certificate: webhookInfo.has_custom_certificate,
      pending_update_count: webhookInfo.pending_update_count,
      last_error_date: webhookInfo.last_error_date,
      last_error_message: webhookInfo.last_error_message,
      max_connections: webhookInfo.max_connections,
      allowed_updates: webhookInfo.allowed_updates,
    });

    if (webhookInfo.url !== webhookUrl) {
      throw new Error(
        `Webhook URL mismatch. Expected: ${webhookUrl}, Got: ${webhookInfo.url}`
      );
    }

    if (
      webhookInfo.pending_update_count &&
      webhookInfo.pending_update_count > 0
    ) {
      log.warn(
        `Warning: ${webhookInfo.pending_update_count} pending updates found`,
        {
          operation: "webhook_setup",
          pending_update_count: webhookInfo.pending_update_count,
        }
      );
    }
  } catch (error) {
    log.error("Failed to set webhook", error, {
      operation: "webhook_setup",
      webhookUrl: `${WEBHOOK_URL}/webhook`,
    });
    throw error;
  }
}

const WEB_APP_URL = process.env.WEB_APP_URL as string;

// Validate WEB_APP_URL
if (!WEB_APP_URL) {
  log.error(
    "WEB_APP_URL is required for bot menu button configuration",
    undefined,
    {
      operation: "bot_startup",
      NODE_ENV,
    }
  );
  process.exit(1);
}

if (!WEB_APP_URL.startsWith("https://")) {
  log.error("WEB_APP_URL must use HTTPS for Telegram Web App", undefined, {
    operation: "bot_startup",
    WEB_APP_URL,
  });
  process.exit(1);
}

log.info("Web App URL validated", {
  operation: "bot_startup",
  WEB_APP_URL: WEB_APP_URL.substring(0, 50) + "...",
});

async function startBot() {
  try {
    log.botLog("Starting Marketplace Bot", {
      operation: "bot_startup",
    });

    // Initialize Redis connection
    log.redisLog("Connecting to Redis", {
      operation: "redis_connect",
    });
    await redisService.connect();

    // Write healthcheck to Redis
    await HealthcheckService.writeInitHealthcheck();

    // Start periodic healthcheck updates
    HealthcheckService.startPeriodicHealthcheck();

    // Start HTTP server for health checks
    log.info("Starting HTTP server", {
      operation: "http_server_start",
      port: PORT,
    });
    await expressHttpServer.start();

    const botInfo = await bot.telegram.getMe();
    log.botLog(`Bot @${botInfo.username} is ready`, {
      operation: "bot_startup",
      botUsername: botInfo.username,
      botId: botInfo.id,
    });

    if (NODE_ENV === "production" && WEBHOOK_URL) {
      log.webhookLog("Setting up webhook for production", {
        operation: "webhook_setup",
        NODE_ENV,
      });
      await setupWebhook();
      log.botLog(`Bot webhook server started on port ${PORT}`, {
        operation: "bot_startup",
        port: PORT,
        mode: "webhook",
      });

      // Mark server as ready for Cloud Run
      expressHttpServer.setReady(true);
    } else {
      log.botLog("Starting bot in polling mode (development)", {
        operation: "bot_startup",
        mode: "polling",
        NODE_ENV,
      });

      log.botLog("Launching bot", {
        operation: "bot_startup",
        mode: "polling",
      });
      bot
        .launch()
        .then(() => {
          log.botLog("Bot started successfully in polling mode", {
            operation: "bot_startup",
            mode: "polling",
            status: "success",
          });
          // Mark server as ready for development
          expressHttpServer.setReady(true);
        })
        .catch((error: any) => {
          log.error("Failed to launch bot", error, {
            operation: "bot_startup",
            mode: "polling",
          });
          if (
            error.message?.includes("409") ||
            error.message?.includes("Conflict")
          ) {
            log.warn(
              "Another bot instance might be running. Please stop it first.",
              {
                operation: "bot_startup",
                mode: "polling",
                error_type: "conflict",
              }
            );
          }
        });
    }

    log.botLog("Setting up bot configuration", {
      operation: "bot_configuration",
    });

    try {
      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: "PREM",
          web_app: {
            url: WEB_APP_URL,
          },
        },
      });

      log.botLog("Menu button configured successfully", {
        operation: "bot_configuration",
        component: "menu_button",
        status: "success",
        menuButtonText: "PREM",
        webAppUrl: WEB_APP_URL.substring(0, 50) + "...",
      });

      // Verify menu button was set correctly
      const menuButton = await bot.telegram.getChatMenuButton();
      log.botLog("Menu button verification", {
        operation: "bot_configuration",
        component: "menu_button_verification",
        menuButtonType: menuButton.type,
        menuButtonText: (menuButton as any).text,
        webAppUrl: (menuButton as any).web_app?.url?.substring(0, 50) + "...",
      });
    } catch (error) {
      log.error("Failed to set menu button", error, {
        operation: "bot_configuration",
        component: "menu_button",
        webAppUrl: WEB_APP_URL,
      });
    }

    try {
      await bot.telegram.setMyCommands([
        { command: "start", description: "Start the bot and show main menu" },
        { command: "help", description: "Show help information" },
        { command: "health", description: "Check bot health status" },
      ]);
      log.botLog("Commands configured", {
        operation: "bot_configuration",
        component: "commands",
        status: "success",
      });
    } catch (error) {
      log.warn("Failed to set commands", {
        operation: "bot_configuration",
        component: "commands",
        error,
      });
    }

    try {
      const description =
        "🛍️ Marketplace Bot - Your gateway to the PREM marketplace platform. Use the PREM button to open the marketplace or send /start to begin!";
      const shortDescription = "🛍️ Open PREM marketplace";

      await bot.telegram.setMyDescription(description);
      await bot.telegram.setMyShortDescription(shortDescription);

      log.botLog("Bot description configured", {
        operation: "bot_configuration",
        component: "description",
        status: "success",
        description: description.substring(0, 100) + "...",
        shortDescription,
      });
    } catch (error) {
      log.error("Failed to set bot description", error, {
        operation: "bot_configuration",
        component: "description",
      });
    }

    log.botLog("Bot setup completed successfully", {
      operation: "bot_startup",
      status: "completed",
      features: [
        "My Buy Orders",
        "My Sell Orders",
        "Get Referral Link",
        "Contact Support",
        "Open Marketplace (Web App)",
      ],
    });
  } catch (error) {
    log.error("Failed to start bot", error, {
      operation: "bot_startup",
    });
    process.exit(1);
  }
}

async function gracefulShutdown(signal: string) {
  log.info(`Received ${signal}, shutting down gracefully`, {
    operation: "bot_shutdown",
    signal,
  });

  // Set a timeout to force exit if graceful shutdown takes too long
  const forceExitTimeout = setTimeout(() => {
    log.error("Graceful shutdown timeout, forcing exit", undefined, {
      operation: "bot_shutdown",
      signal,
    });
    process.exit(1);
  }, 8000); // 8 seconds, leaving 2 seconds buffer before Cloud Run SIGKILL

  try {
    // Stop periodic healthcheck first
    log.info("Stopping periodic healthcheck", {
      operation: "bot_shutdown",
      step: "healthcheck",
    });
    HealthcheckService.stopPeriodicHealthcheck();

    // Stop the bot (fastest operation)
    log.botLog("Stopping bot", {
      operation: "bot_shutdown",
      step: "bot",
    });
    bot.stop(signal);

    // Stop HTTP server (close connections immediately)
    log.info("Stopping HTTP server", {
      operation: "bot_shutdown",
      step: "http_server",
    });
    await expressHttpServer.stop();

    // Disconnect from Redis (can be slow)
    log.redisLog("Disconnecting from Redis", {
      operation: "redis_disconnect",
    });
    await Promise.race([
      redisService.disconnect(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Redis disconnect timeout")), 3000)
      ),
    ]);

    // Skip webhook cleanup in graceful shutdown to save time
    // Webhook will be re-established on next container start
    log.info("Skipping webhook cleanup for faster shutdown", {
      operation: "bot_shutdown",
      step: "webhook_cleanup_skip",
    });

    clearTimeout(forceExitTimeout);
    log.info("Graceful shutdown completed", {
      operation: "bot_shutdown",
      status: "completed",
    });
    process.exit(0);
  } catch (error) {
    clearTimeout(forceExitTimeout);
    log.error("Error during shutdown", error, {
      operation: "bot_shutdown",
      status: "error",
    });
    process.exit(1);
  }
}

process.once("SIGINT", () => gracefulShutdown("SIGINT"));
process.once("SIGTERM", () => gracefulShutdown("SIGTERM"));

startBot();
