import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { UserEntity } from "./types";
import { extractRawTonAddress, prepareUserDataForSave } from "./utils";
import { CORS_CONFIG } from "./config";
import { log } from "./utils/logger";

export const changeUserData = onCall<{
  name?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const {
    name,
    tg_id,
    ton_wallet_address,
    raw_ton_wallet_address,
    referrer_id,
  } = request.data;
  const userId = request.auth.uid;

  try {
    const db = admin.firestore();

    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const currentUserData = userDoc.data() as UserEntity;

    const updateData: Partial<UserEntity> = {};

    // Update name if provided
    if (name !== undefined) {
      updateData.displayName = name;
    }

    // Update Telegram ID if provided
    if (tg_id !== undefined) {
      updateData.tg_id = tg_id;
    }

    // Update TON wallet address if provided
    if (ton_wallet_address !== undefined) {
      updateData.ton_wallet_address = ton_wallet_address;

      // If no raw_ton_wallet_address is provided, create it from ton_wallet_address
      if (raw_ton_wallet_address === undefined && ton_wallet_address) {
        const rawAddress = extractRawTonAddress(ton_wallet_address);
        if (rawAddress) {
          updateData.raw_ton_wallet_address = rawAddress;
        }
      }
    }

    // Update raw TON wallet address if provided
    if (raw_ton_wallet_address !== undefined) {
      updateData.raw_ton_wallet_address = raw_ton_wallet_address;
    }

    // Update referrer ID if provided and user doesn't already have one
    if (referrer_id && !currentUserData.referrer_id) {
      updateData.referrer_id = referrer_id;
      log.info(`Setting referrer_id for user ${userId}: ${referrer_id}`, {
        operation: "user_profile_update",
        userId,
        referrer_id,
        action: "set_referrer",
      });
    } else if (referrer_id && currentUserData.referrer_id) {
      log.info(
        `User ${userId} already has referrer_id: ${currentUserData.referrer_id}, not updating`,
        {
          operation: "user_profile_update",
          userId,
          existing_referrer_id: currentUserData.referrer_id,
          attempted_referrer_id: referrer_id,
          action: "skip_referrer_update",
        }
      );
    }

    // Prepare data for save (handles TON address processing)
    const preparedData = prepareUserDataForSave(updateData);

    // Update user document
    await db.collection("users").doc(userId).update(preparedData);

    log.info(`User profile updated for ${userId}`, {
      operation: "user_profile_update",
      userId,
      updatedFields: Object.keys(preparedData),
      preparedData,
    });

    return {
      success: true,
      message: "User profile updated successfully",
      updatedFields: Object.keys(preparedData),
    };
  } catch (error) {
    log.error("Error in changeUserData function", error, {
      operation: "user_profile_update",
      userId,
      requestData: request.data,
    });

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while updating user profile."
    );
  }
});
