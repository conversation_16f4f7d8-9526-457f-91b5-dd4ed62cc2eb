/* eslint-disable indent */
import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  updateUserBalance,
  validateSufficientFunds,
} from "../services/balance-service";
import {
  applyResellPurchaseFeeWithReferral,
  calculateFeeAmount,
  getAppConfig,
} from "../services/fee-service";
import { OrderEntity, OrderStatus, UserEntity } from "../types";
import { safeMultiply, safeSubtract } from "../utils";
import { CORS_CONFIG } from "../config";
import { log } from "../utils/logger";

export const setSecondaryMarketPrice = onCall<{
  orderId: string;
  secondaryMarketPrice: number;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { orderId, secondaryMarketPrice } = request.data;
  const userId = request.auth?.uid;

  if (!userId) {
    throw new HttpsError("unauthenticated", "User must be authenticated.");
  }

  if (!orderId || typeof orderId !== "string") {
    throw new HttpsError("invalid-argument", "Valid order ID is required.");
  }

  if (
    secondaryMarketPrice === null ||
    secondaryMarketPrice === undefined ||
    typeof secondaryMarketPrice !== "number" ||
    secondaryMarketPrice <= 0
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Valid secondary market price is required."
    );
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (order.status !== OrderStatus.PAID) {
      throw new HttpsError(
        "failed-precondition",
        "Only orders with PAID status can be listed on secondary market."
      );
    }

    if (order.buyerId !== userId) {
      throw new HttpsError(
        "permission-denied",
        "Only the current buyer can set secondary market price."
      );
    }

    if (!order.buyerId || !order.sellerId) {
      throw new HttpsError(
        "failed-precondition",
        "Order must have both buyer and seller to be listed on secondary market."
      );
    }

    const config = await getAppConfig();
    const minSecondaryMarketPrice = config?.min_secondary_market_price ?? 1;

    if (secondaryMarketPrice < minSecondaryMarketPrice) {
      throw new HttpsError(
        "invalid-argument",
        `Secondary market price must be at least ${minSecondaryMarketPrice} TON.`
      );
    }

    await db.collection("orders").doc(orderId).update({
      secondaryMarketPrice,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Secondary market price set to ${secondaryMarketPrice} TON.`,
      orderId,
      secondaryMarketPrice,
    };
  } catch (error) {
    log.error("Error setting secondary market price", error, {
      orderId,
      secondaryMarketPrice,
      operation: "set_secondary_market_price",
    });
    throw new HttpsError(
      "internal",
      (error as any).message ??
        "Server error while setting secondary market price."
    );
  }
});

export const makeSecondaryMarketPurchase = onCall<{
  orderId: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { orderId } = request.data;
  const newBuyerId = request.auth?.uid;

  if (!newBuyerId) {
    throw new HttpsError("unauthenticated", "User must be authenticated.");
  }

  if (!orderId || typeof orderId !== "string") {
    throw new HttpsError("invalid-argument", "Valid order ID is required.");
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (!order.secondaryMarketPrice || order.secondaryMarketPrice <= 0) {
      throw new HttpsError(
        "failed-precondition",
        "Order is not available on secondary market."
      );
    }

    if (order.status !== OrderStatus.PAID) {
      throw new HttpsError(
        "failed-precondition",
        "Only orders with PAID status can be purchased on secondary market."
      );
    }

    if (newBuyerId === order.sellerId) {
      throw new HttpsError(
        "permission-denied",
        "Seller cannot purchase their own order on secondary market."
      );
    }

    if (newBuyerId === order.buyerId) {
      throw new HttpsError(
        "permission-denied",
        "Current buyer cannot purchase their own order on secondary market."
      );
    }

    if (!order.buyerId || !order.sellerId) {
      throw new HttpsError(
        "failed-precondition",
        "Order must have both buyer and seller for secondary market purchase."
      );
    }

    const newBuyerDoc = await db.collection("users").doc(newBuyerId).get();
    if (!newBuyerDoc.exists) {
      throw new HttpsError("not-found", "New buyer not found.");
    }

    const newBuyerData = newBuyerDoc.data() as UserEntity;
    const referralId = newBuyerData?.referrer_id;

    // Use fees from order object instead of app_config
    const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage || 0;
    const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal

    const secondaryMarketPrice = order.secondaryMarketPrice;

    // Calculate old buyer's locked amount (from original order) - this will be transferred to new buyer
    const oldBuyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);

    // Apply resell purchase fee with referral using order fees
    const resellPurchaseFeeBPS = order.fees?.resell_purchase_fee || 0;
    const referrerFeeBPS = order.fees?.referrer_fee || 0;

    const feeResult = await applyResellPurchaseFeeWithReferral({
      buyerId: newBuyerId,
      amount: secondaryMarketPrice,
      referralId,
      resellPurchaseFeeBPS,
      referrerFeeBPS,
    });

    const netAmountToOldBuyer = safeSubtract(
      secondaryMarketPrice,
      feeResult.totalFee
    );

    // Validate new buyer has sufficient funds for secondary market purchase
    await validateSufficientFunds({
      userId: newBuyerId,
      amount: secondaryMarketPrice,
      operation: "secondary market purchase",
    });

    // Step 1: New buyer pays secondary market price and gets locked amount from old buyer
    // New buyer: sum = sum - secondaryMarketPrice + oldBuyerLockedAmount, locked = locked + oldBuyerLockedAmount
    await updateUserBalance({
      userId: newBuyerId,
      sumChange: -secondaryMarketPrice + oldBuyerLockedAmount,
      lockedChange: oldBuyerLockedAmount,
    });

    // Step 2: Old buyer receives net amount and loses their locked amount
    // Old buyer: sum = sum + netAmountToOldBuyer - oldBuyerLockedAmount, locked = locked - oldBuyerLockedAmount
    await updateUserBalance({
      userId: order.buyerId,
      sumChange: netAmountToOldBuyer - oldBuyerLockedAmount,
      lockedChange: -oldBuyerLockedAmount,
    });

    // Step 3: Calculate and accumulate reseller earnings for seller
    const resellPurchaseFeeForSellerBPS =
      order.fees?.resell_purchase_fee_for_seller || 0;
    const resellerEarningsForSeller =
      resellPurchaseFeeForSellerBPS > 0
        ? calculateFeeAmount(
            secondaryMarketPrice,
            resellPurchaseFeeForSellerBPS
          )
        : 0;

    const currentResellerEarnings = order.reseller_earnings_for_seller ?? 0;
    const newResellerEarnings =
      currentResellerEarnings + resellerEarningsForSeller;

    // Update order with new buyer, clear secondary market price, and update reseller earnings
    await db.collection("orders").doc(orderId).update({
      buyerId: newBuyerId,
      secondaryMarketPrice: null,
      reseller_earnings_for_seller: newResellerEarnings,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Secondary market purchase successful! Paid ${secondaryMarketPrice} TON (${feeResult.totalFee} TON fee). ${oldBuyerLockedAmount} TON locked.`,
      orderId,
      secondaryMarketPrice,
      newBuyerId,
      oldBuyerId: order.buyerId,
      netAmountToOldBuyer,
      feeAmount: feeResult.totalFee,
      lockedAmount: oldBuyerLockedAmount,
    };
  } catch (error) {
    log.error("Error making secondary market purchase", error, {
      orderId,
      newBuyerId,
      operation: "secondary_market_purchase",
    });
    throw new HttpsError(
      "internal",
      (error as any).message ??
        "Server error while making secondary market purchase."
    );
  }
});
